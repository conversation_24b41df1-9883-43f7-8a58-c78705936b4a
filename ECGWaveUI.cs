using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ECGWaveUI : MaskableGraphic
{
    public float speed = 100f;
    public float amplitude = 20f;
    public float frequency = 1f;
    public float pointSpacing = 5f;

    private List<Vector2> points = new List<Vector2>();
    private float offset;

    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();

        points.Clear();
        float width = rectTransform.rect.width;
        float height = rectTransform.rect.height / 2f;

        int pointCount = Mathf.CeilToInt(width / pointSpacing) + 1;

        for (int i = 0; i < pointCount; i++)
        {
            float x = i * pointSpacing;
            float t = (x + offset) * 0.01f;

            // Simple ECG waveform pattern
            float y = Mathf.Sin(t * frequency) * amplitude;

            // Add a fake "spike" at a specific t value for realism
            if (Mathf.Repeat(t, 5f) < 0.2f)
                y += Mathf.Sin(t * 20f) * amplitude * 2f;

            points.Add(new Vector2(x, height + y));
        }

        for (int i = 0; i < points.Count - 1; i++)
        {
            DrawLine(vh, points[i], points[i + 1], 2f, Color.green);
        }
    }

    void Update()
    {
        offset += speed * Time.deltaTime;
        SetVerticesDirty();
    }

    void DrawLine(VertexHelper vh, Vector2 start, Vector2 end, float thickness, Color color)
    {
        Vector2 direction = (end - start).normalized;
        Vector2 normal = new Vector2(-direction.y, direction.x) * thickness / 2f;

        UIVertex[] verts = new UIVertex[4];
        verts[0].position = start - normal;
        verts[1].position = start + normal;
        verts[2].position = end + normal;
        verts[3].position = end - normal;

        for (int i = 0; i < 4; i++)
        {
            verts[i].color = color;
        }

        int index = vh.currentVertCount;

        vh.AddUIVertexQuad(verts);
    }
}
